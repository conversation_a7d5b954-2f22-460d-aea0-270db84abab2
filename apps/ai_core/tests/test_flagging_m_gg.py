"""
Unit tests for M-GG inconsistency flagging functionality
"""

import unittest
import json
import tempfile
import os
import re


def flag_m_gg_inconsistencies(responses_data, flagging_rules_path):
    """
    Flag inconsistencies between M and GG questions based on defined rules.

    Args:
        responses_data: List of response objects with question_code and answer_text
        flagging_rules_path: Path to the JSON file containing flagging rules

    Returns:
        Dict: {rule_name: [discrepancy_objects]} where each discrepancy object contains:
              {M_Item, GG_item, isDiscrepant, notes}
    """

    # Load flagging rules
    try:
        with open(flagging_rules_path, 'r') as f:
            flagging_rules = json.load(f)
    except FileNotFoundError:
        return {"error": "Flagging rules file not found"}

    # Create a lookup dictionary for responses by question_code
    response_lookup = {}
    for response in responses_data:
        question_code = response.get("question_code", "").upper()
        response_lookup[question_code] = response

    def extract_numeric_value(answer_text):
        """Extract numeric value from answer text (e.g., '06 - Independent' -> 6)"""
        if not answer_text or not isinstance(answer_text, list) or len(answer_text) == 0:
            return None

        text = str(answer_text[0]) if isinstance(answer_text, list) else str(answer_text)
        # Extract first number from the text
        match = re.match(r'^(\d+)', text.strip())
        if match:
            return int(match.group(1))
        return None

    # Process each rule set
    flagging_results = {}

    for rule_name, rule_data in flagging_rules.items():
        discrepancies = []
        rules = rule_data.get("rules", {})
        question_pairs = rule_data.get("question_pairs", [])

        for pair in question_pairs:
            m_item = pair["M_item"].upper()
            gg_item = pair["GG_item"].upper()
            pair_notes = pair.get("notes", "")

            # Get M item response
            m_response = response_lookup.get(m_item)
            if not m_response:
                discrepancies.append({
                    "M_Item": m_item,
                    "GG_item": gg_item,
                    "isDiscrepant": True,
                    "notes": f"M item {m_item} not found in responses. {pair_notes}"
                })
                continue

            # Get GG item response
            gg_response = response_lookup.get(gg_item)
            if not gg_response:
                discrepancies.append({
                    "M_Item": m_item,
                    "GG_item": gg_item,
                    "isDiscrepant": True,
                    "notes": f"GG item {gg_item} not found in responses. {pair_notes}"
                })
                continue

            # Extract numeric values
            m_value = extract_numeric_value(m_response.get("answer_text"))
            gg_value = extract_numeric_value(gg_response.get("answer_text"))

            if m_value is None:
                discrepancies.append({
                    "M_Item": m_item,
                    "GG_item": gg_item,
                    "isDiscrepant": True,
                    "notes": f"Could not extract numeric value from M item {m_item}. {pair_notes}"
                })
                continue

            if gg_value is None:
                discrepancies.append({
                    "M_Item": m_item,
                    "GG_item": gg_item,
                    "isDiscrepant": True,
                    "notes": f"Could not extract numeric value from GG item {gg_item}. {pair_notes}"
                })
                continue

            # Check rule compliance
            m_value_str = str(m_value)
            allowed_gg_values = rules.get(m_value_str, [])

            is_discrepant = gg_value not in allowed_gg_values

            if is_discrepant:
                discrepancies.append({
                    "M_Item": m_item,
                    "GG_item": gg_item,
                    "isDiscrepant": True,
                    "notes": f"M={m_value}, GG={gg_value}, Expected GG in {allowed_gg_values}. {pair_notes}"
                })
            else:
                # Also include non-discrepant pairs for completeness
                discrepancies.append({
                    "M_Item": m_item,
                    "GG_item": gg_item,
                    "isDiscrepant": False,
                    "notes": f"M={m_value}, GG={gg_value}, Compliant. {pair_notes}"
                })

        flagging_results[rule_name] = discrepancies

    return flagging_results


class TestFlaggingMGG(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures"""
        # Create a temporary rules file for testing
        self.test_rules = {
            "test_rule": {
                "rules": {
                    "0": [6],
                    "1": [5],
                    "2": [2, 3, 4]
                },
                "question_pairs": [
                    {"M_item": "M1810", "GG_item": "GG0130.A", "notes": "Test pair 1"},
                    {"M_item": "M1820", "GG_item": "GG0130.B", "notes": "Test pair 2"}
                ]
            }
        }
        
        # Create temporary file
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.test_rules, self.temp_file)
        self.temp_file.close()
        
    def tearDown(self):
        """Clean up test fixtures"""
        os.unlink(self.temp_file.name)
    
    def test_compliant_responses(self):
        """Test responses that are compliant with rules"""
        responses = [
            {
                "question_code": "M1810",
                "answer_text": ["0 - Independent"]
            },
            {
                "question_code": "GG0130.A",
                "answer_text": ["06 - Independent"]
            },
            {
                "question_code": "M1820", 
                "answer_text": ["1 - Some assistance"]
            },
            {
                "question_code": "GG0130.B",
                "answer_text": ["05 - Supervision"]
            }
        ]
        
        results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
        
        self.assertIn("test_rule", results)
        discrepancies = results["test_rule"]
        
        # Should have 2 pairs, both compliant
        self.assertEqual(len(discrepancies), 2)
        self.assertFalse(discrepancies[0]["isDiscrepant"])
        self.assertFalse(discrepancies[1]["isDiscrepant"])
        
    def test_discrepant_responses(self):
        """Test responses that are discrepant with rules"""
        responses = [
            {
                "question_code": "M1810",
                "answer_text": ["0 - Independent"]  # M=0, should have GG=6
            },
            {
                "question_code": "GG0130.A",
                "answer_text": ["02 - Substantial assistance"]  # GG=2, not in [6]
            }
        ]
        
        results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
        
        self.assertIn("test_rule", results)
        discrepancies = results["test_rule"]
        
        # First pair should be discrepant, second should be missing GG item
        self.assertTrue(discrepancies[0]["isDiscrepant"])
        self.assertIn("M=0, GG=2, Expected GG in [6]", discrepancies[0]["notes"])
        
    def test_missing_m_item(self):
        """Test when M item is missing from responses"""
        responses = [
            {
                "question_code": "GG0130.A",
                "answer_text": ["06 - Independent"]
            }
        ]
        
        results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
        
        discrepancies = results["test_rule"]
        self.assertTrue(discrepancies[0]["isDiscrepant"])
        self.assertIn("M item M1810 not found", discrepancies[0]["notes"])
        
    def test_missing_gg_item(self):
        """Test when GG item is missing from responses"""
        responses = [
            {
                "question_code": "M1810",
                "answer_text": ["0 - Independent"]
            }
        ]
        
        results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
        
        discrepancies = results["test_rule"]
        self.assertTrue(discrepancies[0]["isDiscrepant"])
        self.assertIn("GG item GG0130.A not found", discrepancies[0]["notes"])
        
    def test_invalid_answer_format(self):
        """Test when answer text cannot be parsed"""
        responses = [
            {
                "question_code": "M1810",
                "answer_text": ["Not a number - Invalid"]
            },
            {
                "question_code": "GG0130.A",
                "answer_text": ["06 - Independent"]
            }
        ]
        
        results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
        
        discrepancies = results["test_rule"]
        self.assertTrue(discrepancies[0]["isDiscrepant"])
        self.assertIn("Could not extract numeric value from M item", discrepancies[0]["notes"])
        
    def test_missing_rules_file(self):
        """Test when rules file doesn't exist"""
        responses = []
        results = flag_m_gg_inconsistencies(responses, "nonexistent_file.json")
        
        self.assertIn("error", results)
        self.assertEqual(results["error"], "Flagging rules file not found")
        
    def test_numeric_extraction(self):
        """Test numeric value extraction from various answer formats"""
        responses = [
            {
                "question_code": "M1810",
                "answer_text": ["2 - Partial assistance"]
            },
            {
                "question_code": "GG0130.A",
                "answer_text": ["03 - Moderate assistance"]
            }
        ]
        
        results = flag_m_gg_inconsistencies(responses, self.temp_file.name)
        
        discrepancies = results["test_rule"]
        # M=2 should allow GG in [2,3,4], so GG=3 should be compliant
        self.assertFalse(discrepancies[0]["isDiscrepant"])
        self.assertIn("M=2, GG=3, Compliant", discrepancies[0]["notes"])


if __name__ == '__main__':
    unittest.main()
